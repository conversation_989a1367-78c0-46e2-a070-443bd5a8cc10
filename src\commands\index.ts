import * as vscode from 'vscode';
import { ChatViewProvider } from '../providers/ChatViewProvider';

/**
 * 注册所有扩展命令
 * @param context 扩展上下文
 * @param chatViewProvider 聊天视图提供者实例
 */
export function registerCommands(context: vscode.ExtensionContext, chatViewProvider: ChatViewProvider) {
	// Hello World 命令（示例命令）
	const helloWorldCommand = vscode.commands.registerCommand('ai-extension-demo.helloWorld', () => {
		vscode.window.showInformationMessage('Hello World from ai_extension_demo!');
	});

	// AI 聊天相关命令
	const newChatCommand = vscode.commands.registerCommand('ai-chat.newChat', () => {
		chatViewProvider.sendMessage({ type: 'newChat' });
	});

	const clearChatCommand = vscode.commands.registerCommand('ai-chat.clearChat', () => {
		chatViewProvider.sendMessage({ type: 'clearChat' });
	});

	const exportChatCommand = vscode.commands.registerCommand('ai-chat.exportChat', () => {
		chatViewProvider.sendMessage({ type: 'exportChat' });
	});

	const showHistoryCommand = vscode.commands.registerCommand('ai-chat.showHistory', () => {
		vscode.window.showInformationMessage('聊天历史功能即将推出！');
	});

	const settingsCommand = vscode.commands.registerCommand('ai-chat.settings', () => {
		chatViewProvider.sendMessage({ type: 'showSettings' });
	});

	// 将所有命令添加到订阅列表
	context.subscriptions.push(
		helloWorldCommand,
		newChatCommand,
		clearChatCommand,
		exportChatCommand,
		showHistoryCommand,
		settingsCommand
	);

	console.log('All commands registered successfully');
}
